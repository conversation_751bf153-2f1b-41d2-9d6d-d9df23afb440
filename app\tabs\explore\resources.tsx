import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Linking,
  Platform,
} from 'react-native';
import { Searchbar, Card } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { format, parseISO, Locale } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { toZonedTime } from 'date-fns-tz';
import { useRouter, useFocusEffect, useLocalSearchParams } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import { organizationStore } from 'stores/organization_store';
import { authenticationStore } from 'stores/authentication_store';
import { createTheme } from 'theme/index';
import {
  ResourceListPullRequest,
  ResourceListPayload,
} from '@/api/api_config';
import { useFetchResourceList } from '@/api/resources_services';
import { AuthModal } from '@/common_modules/AuthModal';
import { ErrorView } from '@/common_modules/ErrorView';

const ALL_ORGANIZATIONS_ID = '00000000-0000-0000-0000-000000000002';
const PAGE_SIZE = 10;

const RenderResourceCard = ({ resource, router, i18n, handleResourcePress, styles: propStyles }: {
  resource: ResourceListPayload,
  router: any, 
  i18n: any, 
  handleResourcePress: (action: () => void) => void,
  styles: any 
}) => {
  const getLocale = (language: string): Locale => {
    switch (language.toLowerCase().split('-')[0]) {
      case 'zh':
        return zhCN;
      default:
        return enUS;
    }
  };

  const formatResourceDate = (dateString: string | undefined): string => {
    if (!dateString) return '';
    try {
      const timeZone = 'Asia/Hong_Kong';
      const parsedDate = parseISO(dateString);
      const zonedDate = toZonedTime(parsedDate, timeZone);
      return format(zonedDate, 'yyyy-MM-dd HH:mm', { locale: getLocale(i18n.language) });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return '';
    }
  };

  const handlePress = () => {
    router.push({
      pathname: `/explore/resources/ResourceDetailScreen`,
      params: { 
        resourceId: resource.id, 
        resourceOrgId: resource.organization_id,
        resourceData: JSON.stringify(resource)
      },
    });
  };
  const iconColor = propStyles.metaText.color; // Use themed color for icons

  return (
    <Card key={resource.id} style={propStyles.card} mode="elevated">
      <TouchableOpacity
        onPress={() => handleResourcePress(handlePress)}
        activeOpacity={0.7}
      >
        <View style={propStyles.cardWrapper}>
          <Card.Content style={propStyles.cardContent}>
            <View style={propStyles.contentContainer}>
              <Text style={propStyles.title} numberOfLines={2}>
                {resource.title}
              </Text>
              <Text style={propStyles.description} numberOfLines={2}>
                {resource.description}
              </Text>
              <View style={propStyles.metaInfo}>
                <View style={propStyles.metaItem}>
                  <MaterialCommunityIcons
                    name="file-multiple-outline"
                    size={16}
                    color={iconColor}
                  />
                  <Text style={propStyles.metaText}>
                    {resource.files && resource.files.length > 0
                      ? resource.files.length > 1
                        ? `${resource.files.length} ${i18n.t('resources.files')}`
                        : `1 ${i18n.t('resources.file')}`
                      : `0 ${i18n.t('resources.files')}`}
                  </Text>
                </View>
                <View style={propStyles.metaItem}>
                  <MaterialCommunityIcons
                    name="clock-outline"
                    size={16}
                    color={iconColor}
                  />
                  <Text style={propStyles.metaText}>
                    {formatResourceDate(resource.updated_at)}
                  </Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </View>
      </TouchableOpacity>
    </Card>
  );
};

export default function ResourcesScreen() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{ organizationId?: string, focusSearch?: string }>();

  const storeTheme = appStyleStore(state => state.theme);
  const activeTheme = storeTheme || createTheme('red');
  const contextSelectedOrgId = organizationStore(state => state.selectedOrganization?.id);
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const searchBarRef = useRef<any>(null);

  const [searchQueryInput, setSearchQueryInput] = useState(''); // For direct Searchbar input
  const [searchQuery, setSearchQuery] = useState(''); // For API query
  const [offset, setOffset] = useState(0);
  const [allResources, setAllResources] = useState<ResourceListPayload[]>([]); 
  const [hasMoreResources, setHasMoreResources] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);
  
  // Simplified loading states (following posts.tsx pattern)
  const [isMainLoading, setIsMainLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // RefreshControl state (separate from main loading)
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const [currentOrgId, setCurrentOrgId] = useState<string | undefined>(undefined);
  
  const dynamicStyles = getThemedStyles(activeTheme);

  useEffect(() => {
    let effectiveOrgId: string | undefined = undefined;
    if (params?.organizationId) {
      effectiveOrgId = params.organizationId;
    } else if (contextSelectedOrgId) {
      effectiveOrgId = contextSelectedOrgId;
    }
    
    if (currentOrgId !== effectiveOrgId) {
      console.log('[ResourcesScreen] Setting currentOrgId to:', effectiveOrgId);
      setCurrentOrgId(effectiveOrgId);
      setOffset(0);
      setAllResources([]);
    }
  }, [params?.organizationId, contextSelectedOrgId, currentOrgId]);

  const currentResourceListParams = React.useMemo<ResourceListPullRequest>(() => {
    const params: ResourceListPullRequest = {
      search_term: searchQuery || undefined,
      limit: PAGE_SIZE,
      offset: offset,
      visibility: 'public',
      status: 'published',
    };

    if (currentOrgId && currentOrgId !== ALL_ORGANIZATIONS_ID) {
      params.organization_id = currentOrgId;
    } else {
      params.organization_id = undefined;
    }

    if (currentOrgId === ALL_ORGANIZATIONS_ID) {
      params.organization_id2 = undefined;
    } else {
      params.organization_id2 = ALL_ORGANIZATIONS_ID;
    }

    return params;
  }, [searchQuery, offset, currentOrgId]);

  const {
    data: resourceData,
    isLoading: isLoadingResources,
    isFetching: isFetchingResources,
    error: resourcesError,
    refetch: refetchResources,
  } = useFetchResourceList(currentResourceListParams);

  useEffect(() => {
    if (resourceData) {
      const newResources = resourceData as ResourceListPayload[];
      if (offset === 0) {
        // Always replace data when offset is 0 (fresh fetch or refresh)
        setAllResources(newResources);
        // Only set main loading to false if we're not refreshing
        if (!isRefreshing) {
          setIsMainLoading(false);
        }
      } else {
        // Append data for pagination
        setAllResources(prevResources => [...prevResources, ...newResources]);
        setIsLoadingMore(false);
      }
      setHasMoreResources(newResources.length === PAGE_SIZE);
    }
  }, [resourceData, offset, isRefreshing]);

  // Update loading states based on React Query states (following posts.tsx pattern)
  useEffect(() => {
    // Don't show main loading when refreshing - only use native refresh indicator
    if (isRefreshing) {
      return;
    }
    
    if (offset === 0) {
      setIsMainLoading(isLoadingResources || isFetchingResources);
    } else {
      setIsLoadingMore(isFetchingResources);
    }
  }, [isLoadingResources, isFetchingResources, offset, isRefreshing]);

  useFocusEffect(
    useCallback(() => {
      if (params?.focusSearch === 'true' && searchBarRef.current) {
        searchBarRef.current.focus();
      }
    }, [params?.focusSearch])
  );

  const handleSearchTextChange = (text: string) => {
    setSearchQueryInput(text);
  };

  const handleSearchSubmit = () => {
    // Show loading state for search
    setIsMainLoading(true);
    setSearchQuery(searchQueryInput);
    setOffset(0);
    // Clear resources for search to show loading immediately
    setAllResources([]);
  };

  const handleSearchClear = () => {
    // Show loading state for search clear
    setIsMainLoading(true);
    setSearchQueryInput('');
    setSearchQuery('');
    setOffset(0);
    // Clear resources for search clear to show loading immediately
    setAllResources([]);
  };

  const handleRetry = () => {
    setIsMainLoading(true);
    setOffset(0);
    setAllResources([]);
    refetchResources();
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    // Reset offset for refresh, but keep existing data visible during refresh
    setOffset(0);
    // Don't clear allResources immediately to prevent blank screen flash
    try {
      await refetchResources();
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchResources]);

  const handleLoadMore = useCallback(() => {
    // Only trigger load more if we have resources, no errors, not loading, and still have more data
    if (!isLoadingMore && hasMoreResources && !isMainLoading && allResources.length > 0 && !resourcesError) {
      setIsLoadingMore(true);
      setOffset(prev => prev + PAGE_SIZE);
    }
  }, [isLoadingMore, hasMoreResources, isMainLoading, allResources.length, resourcesError]);

  const onScroll = useCallback(({ nativeEvent }: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom) {
      handleLoadMore();
    }
  }, [handleLoadMore]);

  const handleResourcePress = useCallback((action: () => void) => {
    if (!isAuthenticated) {
      setPendingAction(() => action);
      setShowAuthModal(true);
    } else {
      action();
    }
  }, [isAuthenticated]);

  const handleLogin = () => {
    setShowAuthModal(false);
    router.push('/tabs/login');
  };
  
  const handleAuthModalClose = () => {
    setShowAuthModal(false);
    setPendingAction(null); 
  };

  return (
    <View style={dynamicStyles.container}>
      <View style={dynamicStyles.header}>
        <Searchbar
          ref={searchBarRef}
          placeholder={t('explore.searchPlaceholder.resources')}
          onChangeText={handleSearchTextChange}
          value={searchQueryInput}
          style={dynamicStyles.searchBar}
          inputStyle={dynamicStyles.searchInput}
          iconColor={activeTheme.system.secondaryText}
          placeholderTextColor={activeTheme.system.secondaryText}
          onSubmitEditing={handleSearchSubmit}
          onClearIconPress={handleSearchClear}
          returnKeyType="search"
        />
      </View>

      {/* Loading state - outside ScrollView for better performance */}
      {isMainLoading && allResources.length === 0 && !isRefreshing ? (
        <View style={dynamicStyles.centeredMessageContainer}>
          <ActivityIndicator size="large" color={activeTheme.colors.primary} />
          <Text style={dynamicStyles.loadingText}>{t('common.loading')}</Text>
        </View>
      ) : !isMainLoading && resourcesError && !isRefreshing ? (
        /* Error state - outside ScrollView, no scrolling needed */
        <ErrorView onRetry={handleRetry} />
      ) : (
        /* Content state - inside ScrollView for normal scrolling */
        <ScrollView
          style={dynamicStyles.resourcesList}
          contentContainerStyle={dynamicStyles.resourcesListContent}
          onScroll={onScroll}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={activeTheme.colors.primary}
              colors={[activeTheme.colors.primary]}
            />
          }
        >
          {!isMainLoading && allResources.length === 0 ? (
            <View style={dynamicStyles.centeredMessageContainer}>
              <MaterialCommunityIcons name="file-document-outline" size={64} color={activeTheme.system.secondaryText} />
              <Text style={dynamicStyles.noResourcesTitle}>{t('explore.noResourcesTitle')}</Text>
              <Text style={dynamicStyles.noResourcesText}>{t('explore.noResourcesText')}</Text>
            </View>
          ) : (
            allResources.map(resource => (
              <RenderResourceCard
                key={resource.id}
                resource={resource}
                router={router}
                i18n={i18n}
                handleResourcePress={handleResourcePress}
                styles={dynamicStyles}
              />
            ))
          )}

          {isLoadingMore && (
            <View style={dynamicStyles.loadingMoreContainer}>
              <ActivityIndicator size="small" color={activeTheme.colors.primary} />
              <Text style={dynamicStyles.loadingMoreText}>{t('explore.loadingMore')}</Text>
            </View>
          )}
        </ScrollView>
      )}

      {showAuthModal && (
        <AuthModal
          visible={showAuthModal}
          onClose={handleAuthModalClose}
          onLogin={handleLogin}
        />
      )}
    </View>
  );
}

const getThemedStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 16,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  searchBar: {
    elevation: 0,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    height: 42,
  },
  searchInput: {
    fontSize: 15,
    minHeight: 0, 
    lineHeight: Platform.OS === 'ios' ? undefined : 18,
    color: theme.system.text,
  },
  resourcesList: {
    flex: 1,
  },
  resourcesListContent: {
    padding: 16,
    gap: 16,
  },
  card: {
    borderRadius: 12,
    backgroundColor: theme.colors.background,
    marginBottom: 4,
  },
  cardWrapper: {},
  cardContent: {
    padding: 12,
  },
  contentContainer: {},
  title: {
    fontSize: 17,
    fontWeight: '600',
    color: theme.system.text,
    marginBottom: 4,
    lineHeight: 22,
  },
  description: {
    fontSize: 14,
    color: theme.system.secondaryText,
    marginBottom: 8,
    lineHeight: 20,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    color: theme.system.secondaryText,
    fontSize: 13,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  centeredMessageContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
  },
  noResultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.system.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noResultsText: {
    fontSize: 15,
    color: theme.system.secondaryText,
    textAlign: 'center'
  },
  noResourcesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.system.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noResourcesText: {
    fontSize: 15,
    color: theme.system.secondaryText,
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 15,
    color: theme.system.secondaryText,
    marginTop: 16,
  },
  errorText: {
    fontSize: 15,
    color: theme.system.text,
    marginTop: 16,
  },
  retryButton: {
    padding: 12,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.colors.onPrimary,
  },
  loadingMoreText: {
    fontSize: 15,
    color: theme.system.text,
    marginLeft: 8,
  },
});
