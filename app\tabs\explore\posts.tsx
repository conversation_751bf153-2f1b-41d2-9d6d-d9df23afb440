import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  RefreshControl,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Image,
} from 'react-native';
import { Searchbar, Card, Chip, Portal, Modal, Button, RadioButton } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { format, parseISO, startOfDay as dfnsStartOfDay, addDays as dfnsAddDays, endOfDay as dfnsEndOfDay, subDays as dfnsSubDays, Locale } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';
import { appStyleStore } from 'stores/app_style_store';
import { organizationStore } from 'stores/organization_store';
import {
  MEDIA_BASE_URL,
  PostListPayload,
  PostTagPayload,
  PublishedPostsListPullRequest,
} from '@/api/api_config';
import { createTheme } from 'theme/index';
import { useFetchExplorePosts, useFetchPostTags } from '@/api/posts_services';
import { postTagsStore } from 'stores/posts_store';
import { ErrorView } from '@/common_modules/ErrorView';

// Define ALL_ORGANIZATIONS_ID constant to match restored version
const ALL_ORGANIZATIONS_ID = '00000000-0000-0000-0000-000000000002';

interface FilterState {
  searchText: string;
  postTypes: string[];
  dateRange: string[];
  orgId?: string;
}

// Helper function to get tag name based on current language
const getTagName = (tag: PostTagPayload, language: string): string => {
  if (!tag) return '';
  if (language.startsWith('zh')) {
    if (language.includes('HK')) {
      return tag.name_zh_hk || tag.name_en || '';
    } else {
      return tag.name_zh_cn || tag.name_en || '';
    }
  } else {
    return tag.name_en || '';
  }
};

export default function PostsScreen() {
  const { t, i18n } = useTranslation();
  const storeTheme = appStyleStore(state => state.theme);
  const activeTheme = storeTheme || createTheme('red');
  const contextSelectedOrgId = organizationStore(state => state.selectedOrganization?.id);
  const router = useRouter();
  const params = useLocalSearchParams<{ focusSearch?: string; filters?: string; organizationId?: string }>();
  const searchBarRef = useRef<any>(null);
  const [filterVisible, setFilterVisible] = useState(false);
  
  // Store subscriptions (following events.tsx pattern)
  const availablePostTags: PostTagPayload[] = postTagsStore(state => state.postTags?.tags || []);
  const isLoadingTags = postTagsStore(state => state.isFetching);
  const tagsError = postTagsStore(state => state.error);

  // Fetch post tags
  const postTagsQuery = useFetchPostTags();
  // UI state (immediate updates)
  const [displaySearchText, setDisplaySearchText] = useState('');
  const [displayFilters, setDisplayFilters] = useState<FilterState>({
    searchText: '',
    postTypes: [],
    dateRange: ['last30Days'],
    orgId: undefined,
  });
  
  // API state (debounced updates)
  const [apiFilters, setApiFilters] = useState<FilterState>({
    searchText: '',
    postTypes: [],
    dateRange: ['last30Days'],
    orgId: undefined,
  });
  
  const [tempFilters, setTempFilters] = useState<FilterState>(displayFilters);
  const [offset, setOffset] = useState(0);
  const [allPosts, setAllPosts] = useState<PostListPayload[]>([]);
  const [hasMorePosts, setHasMorePosts] = useState(true);
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});
  
  // Simplified loading states
  const [isMainLoading, setIsMainLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // RefreshControl state (separate from main loading)
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const PAGE_SIZE = 20;

  // Debounce refs for different operations
  const filterDebounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const getLocale = (language: string): Locale => {
    switch (language.toLowerCase().split('-')[0]) {
      case 'zh':
        return zhCN;
      default:
        return enUS;
    }
  };

  const getDateRangeFromPreset = useCallback((preset: string): { start_date?: string; end_date?: string } => {
    if (preset === 'all' || !preset) return {};
    const timeZone = 'Asia/Hong_Kong';
    const nowInHKT = toZonedTime(new Date(), timeZone);
    let startDate: Date;
    let endDate: Date;

    switch (preset) {
      case 'latest':
        startDate = dfnsSubDays(nowInHKT, 1);
        endDate = nowInHKT;
        break;
      case 'last7Days':
        startDate = dfnsStartOfDay(dfnsSubDays(nowInHKT, 6));
        endDate = dfnsEndOfDay(nowInHKT);
        break;
      case 'last30Days':
        startDate = dfnsStartOfDay(dfnsSubDays(nowInHKT, 29));
        endDate = dfnsEndOfDay(nowInHKT);
        break;
      default:
        return {};
    }
    
    return {
      start_date: fromZonedTime(startDate, timeZone).toISOString(),
      end_date: fromZonedTime(endDate, timeZone).toISOString(),
    };
  }, []);
  
  const currentQueryParams = React.useMemo<PublishedPostsListPullRequest>(() => {
    const { start_date, end_date } = getDateRangeFromPreset(apiFilters.dateRange[0]);
    
    const params: PublishedPostsListPullRequest = {
      limit: PAGE_SIZE,
      offset,
      status: 'published',
      search_term: apiFilters.searchText,
      tagIds: apiFilters.postTypes.length > 0 ? apiFilters.postTypes : undefined,
      start_date,
      end_date,
    };

    // Handle organization_id based on dropdown selection (matches restored version logic)
    if (apiFilters.orgId && apiFilters.orgId !== ALL_ORGANIZATIONS_ID) {
      params.organization_id = apiFilters.orgId; // Specific org selected
    } else {
      params.organization_id = undefined; // "All Organizations" selected or no org filter
    }

    // Handle organization_id2 based on dropdown selection (matches restored version logic)
    if (apiFilters.orgId === ALL_ORGANIZATIONS_ID) {
      // "All Organizations" is EXPLICITLY selected in dropdown
      params.organization_id2 = undefined;
    } else {
      // Specific org selected in dropdown OR no org selected in dropdown (apiFilters.orgId is undefined)
      params.organization_id2 = ALL_ORGANIZATIONS_ID;
    }

    return params;
  }, [apiFilters, offset, getDateRangeFromPreset, PAGE_SIZE]);

  const {
    data: postsData,
    isLoading: isLoadingPosts,
    isFetching: isFetchingPosts,
    error: postsError,
  } = useFetchExplorePosts(currentQueryParams);

  useEffect(() => {
    if (postsData) {
      const newPosts = postsData as PostListPayload[];
      if (offset === 0) { 
        // Always replace data when offset is 0 (fresh fetch or refresh)
        setAllPosts(newPosts);
        // Only set main loading to false if we're not refreshing
        if (!isRefreshing) {
          setIsMainLoading(false);
        }
      } else { 
        // Append data for pagination
        setAllPosts(prevPosts => [...prevPosts, ...newPosts]);
        setIsLoadingMore(false);
      }
      setHasMorePosts(newPosts.length === PAGE_SIZE);
    }
  }, [postsData, offset, isRefreshing]);

  // Update loading states based on React Query states
  useEffect(() => {
    // Don't show main loading when refreshing - only use native refresh indicator
    if (isRefreshing) {
      return;
    }
    
    if (offset === 0) {
      setIsMainLoading(isLoadingPosts || isFetchingPosts);
    } else {
      setIsLoadingMore(isFetchingPosts);
    }
  }, [isLoadingPosts, isFetchingPosts, offset, isRefreshing]);

  // Debounced API update for filters
  const updateApiFiltersWithFilterDebounce = useCallback((newFilters: FilterState) => {
    // Show loading state and clear data for filter changes
    setIsMainLoading(true);
    setOffset(0); 
    setAllPosts([]);
    
    // Debounce only the API call
    if (filterDebounceTimerRef.current) clearTimeout(filterDebounceTimerRef.current);
    filterDebounceTimerRef.current = setTimeout(() => {
      console.log('[PostsScreen] Executing debounced filter API call for:', newFilters);
      setApiFilters(newFilters);
    }, 1000);
  }, []);

  // Immediate API update (no debounce)
  const updateApiFiltersImmediately = useCallback((newFilters: FilterState) => {
    // Check if filters actually changed to avoid unnecessary loading state
    const filtersChanged = JSON.stringify(newFilters) !== JSON.stringify(apiFilters);
    if (!filtersChanged) {
      return; // No change, no need to update
    }
    // Show loading state and clear data for filter/search changes
    setIsMainLoading(true);
    setOffset(0); 
    setAllPosts([]);
    setApiFilters(newFilters);
  }, [apiFilters]);

  useEffect(() => {
    if (params?.focusSearch === 'true' && searchBarRef.current) {
      const timer = setTimeout(() => { searchBarRef.current?.focus(); }, 100);
      return () => clearTimeout(timer);
    }
  }, [params?.focusSearch]);

  useEffect(() => {
    if (params?.filters) {
      try {
        const parsedFilters = JSON.parse(params.filters as string);
        const newFiltersFromParams: Partial<FilterState> = {
          dateRange: parsedFilters.dateRange || displayFilters.dateRange,
          postTypes: Array.isArray(parsedFilters.postTypes) ? parsedFilters.postTypes : displayFilters.postTypes,
          searchText: parsedFilters.searchText !== undefined ? parsedFilters.searchText : displayFilters.searchText,
        };
        const updatedFilters = { ...displayFilters, ...newFiltersFromParams };
        setDisplayFilters(updatedFilters);
        setTempFilters(updatedFilters);
        setDisplaySearchText(updatedFilters.searchText);
        updateApiFiltersImmediately(updatedFilters);
      } catch (e) {
        console.error("Failed to parse filters from params", e);
      }
    }
  }, [params?.filters, updateApiFiltersImmediately]);

  useEffect(() => {
    let effectiveOrgId: string | undefined = undefined;
    if (params?.organizationId) {
      effectiveOrgId = params.organizationId;
    } else if (contextSelectedOrgId) {
      effectiveOrgId = contextSelectedOrgId;
    }    
    if (displayFilters.orgId !== effectiveOrgId) {
        const updatedFilters = { ...displayFilters, orgId: effectiveOrgId };
        setDisplayFilters(updatedFilters);
        setTempFilters(updatedFilters);
        updateApiFiltersImmediately(updatedFilters);
    }
  }, [params?.organizationId, contextSelectedOrgId, displayFilters, updateApiFiltersImmediately]);

  const dateRangeOptions = [
    { id: 'all', label: t('explore.filters.date.all') },
    { id: 'latest', label: t('explore.filters.date.latest') },
    { id: 'last7Days', label: t('explore.filters.date.last7Days') },
    { id: 'last30Days', label: t('explore.filters.date.last30Days') },
  ];

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    // Reset offset for refresh, but keep existing data visible during refresh
    setOffset(0);
    // Don't clear allPosts immediately to prevent blank screen flash
    try {
      // Force a refetch by creating a new query key effect
      const refreshFilters = { ...apiFilters };
      setApiFilters(refreshFilters);
    } finally {
      setIsRefreshing(false);
    }
  }, [apiFilters]);

  const handleLoadMore = useCallback(() => {
    // Only trigger load more if we have posts, no errors, not loading, and still have more data
    if (!isLoadingMore && hasMorePosts && !isMainLoading && allPosts.length > 0 && !postsError) {
      setIsLoadingMore(true);
      setOffset(prev => prev + PAGE_SIZE);
    }
  }, [isLoadingMore, hasMorePosts, isMainLoading, allPosts.length, postsError]);

  // Search handling: immediate UI update + API call only on submit
  const handleSearchChange = (text: string) => {
    // Immediate UI update only
    setDisplaySearchText(text);
    setDisplayFilters(prev => ({ ...prev, searchText: text }));
  };
  
  const handleSearchSubmit = () => {
    // Apply current search text immediately
    updateApiFiltersImmediately({ ...displayFilters, searchText: displaySearchText });
  };

  const handleSearchClear = () => {
    // Immediate UI update and API call
    setDisplaySearchText('');
    const updatedFilters = { ...displayFilters, searchText: '' };
    setDisplayFilters(updatedFilters);
    updateApiFiltersImmediately(updatedFilters);
  };

  const showFilterModal = () => { setTempFilters(displayFilters); setFilterVisible(true); };
  const hideFilterModal = () => setFilterVisible(false);

  const applyFilters = () => {
    console.log('[PostsScreen - applyFilters] Applying tempFilters:', tempFilters);
    setDisplayFilters(tempFilters);
    setDisplaySearchText(tempFilters.searchText);
    updateApiFiltersImmediately(tempFilters);
    hideFilterModal();
  };

  const resetFilters = () => {
    const defaultFilters: FilterState = {
      searchText: '',
      postTypes: [],
      dateRange: ['last30Days'],
      orgId: contextSelectedOrgId ? contextSelectedOrgId : undefined,
    };
    setTempFilters(defaultFilters);
  };

  // Filter chip removal: immediate UI update + debounced API call
  const handleRemoveFilter = (filterType: keyof FilterState, value?: string) => {
    let updatedFilters = { ...displayFilters };
    
    if (filterType === 'postTypes' && value) {
      updatedFilters.postTypes = updatedFilters.postTypes.filter(id => id !== value);
    } else if (filterType === 'dateRange') {
      updatedFilters.dateRange = ['all'];
    }
    
    setDisplayFilters(updatedFilters);
    updateApiFiltersWithFilterDebounce(updatedFilters);
  };

  const onScroll = useCallback(({ nativeEvent }: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom) {
        handleLoadMore();
    }
  }, [handleLoadMore]);

  const handleDateRangeSelect = (optionId: string) => {
    setTempFilters(prev => ({ ...prev, dateRange: [optionId] }));
  };

  const handleImageError = (postId: string) => {
    setImageErrors(prev => ({ ...prev, [postId]: true }));
  };

  const renderPostCard = (post: PostListPayload) => {
    let coverImageUrl = post?.media_items?.find(item => item.is_banner)?.file_path;
    if (coverImageUrl) {
      const url = new URL(coverImageUrl, MEDIA_BASE_URL);
      coverImageUrl = new URL(url.pathname, MEDIA_BASE_URL).toString();
    }
    const hasImageError = imageErrors[post.id] || false;
    const handleImageLoadError = (_error: any) => { handleImageError(post.id); };

    const formatPostDate = (dateString: string | undefined): string => {
      if (!dateString) return t('common.noDate');
      try {
        const timeZone = 'Asia/Hong_Kong';
        const parsedDate = parseISO(dateString);
        const zonedDate = toZonedTime(parsedDate, timeZone);
        return format(zonedDate, 'yyyy-MM-dd HH:mm', { locale: getLocale(i18n.language) });
      } catch (error) {
        console.warn('Error formatting date:', error);
        return t('common.noDate');
      }
    };

    return (
        <TouchableOpacity
        key={post.id}
        onPress={() => router.push({
          pathname: '/explore/posts/PostsDetailScreen',
          params: { postsId: post.id, postsOrgId: post.organization_id }
        })}
        style={styles.cardWrapper}
      >
        <Card style={styles.card}>
          <View style={styles.cardImageContainer}>
            {coverImageUrl && !hasImageError ? (
              <Image source={{ uri: coverImageUrl, cache: 'reload' }} style={styles.cardImage} onError={handleImageLoadError} resizeMode="cover" />
            ) : (
              <View style={[styles.cardImage, { backgroundColor: activeTheme.colors.primaryContainer, alignItems: 'center', justifyContent: 'center' }]}>
                <MaterialCommunityIcons name="image-off" size={48} color="#666666" />
              </View>
            )}
          </View>
            <Card.Content style={styles.cardContent}>
              <Text style={styles.title} numberOfLines={2}>{post.title}</Text>
              <View style={styles.metaInfo}>
                <View style={styles.metaItem}>
                  <MaterialCommunityIcons name="account" size={16} color="#666666" />
                  <Text style={styles.metaText}>{post.author_display_name || t('common.noAuthor')}</Text>
                </View>
                <View style={styles.metaItem}>
                  <MaterialCommunityIcons name="clock-outline" size={16} color="#666666" />
                  <Text style={styles.metaText}>
                    {formatPostDate(post.updated_at)}
                  </Text>
                </View>
              </View>
            </Card.Content>
        </Card>
        </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Searchbar
            ref={searchBarRef}
            placeholder={t('explore.searchPlaceholder.posts')}
            onChangeText={handleSearchChange}
            value={displaySearchText}
            style={styles.searchBar}
            returnKeyType="search"
            onSubmitEditing={handleSearchSubmit}
            onClearIconPress={handleSearchClear}
            inputStyle={styles.searchInput}
            iconColor="#666666"
            placeholderTextColor="#666666"
          />
          <TouchableOpacity onPress={showFilterModal} style={styles.filterButton}>
            <MaterialCommunityIcons name="filter-variant" size={24} color={activeTheme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {(displayFilters.postTypes.length > 0 || (displayFilters.dateRange.length > 0 && displayFilters.dateRange[0] !== 'all')) && (
        <View style={styles.filterTags}>
          {displayFilters.postTypes.map(typeId => {
            const tag = availablePostTags.find((t: PostTagPayload) => t.id === typeId);
            return (
              <Chip
                key={typeId}
                mode="outlined"
                onClose={() => handleRemoveFilter('postTypes', typeId)}
                style={styles.filterChip}
              >
                {tag ? getTagName(tag, i18n.language) : t('common.unknown')}
              </Chip>
            );
          })}
          {displayFilters.dateRange[0] !== 'all' && (
                    <Chip
              key={displayFilters.dateRange[0]}
              mode="outlined"
              onClose={() => handleRemoveFilter('dateRange')}
              style={styles.filterChip}
            >
              {dateRangeOptions.find(d => d.id === displayFilters.dateRange[0])?.label || displayFilters.dateRange[0]}
                    </Chip>
          )}
        </View>
      )}

      {/* Loading state - outside ScrollView for better performance */}
      {isMainLoading && allPosts.length === 0 && !isRefreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={activeTheme.colors.primary} />
          <Text style={styles.loadingText}>{t('common.loading')}</Text>
        </View>
      ) : !isMainLoading && postsError && !isRefreshing ? (
        /* Error state - outside ScrollView, no scrolling needed */
        <ErrorView 
          onRetry={() => {
            setIsMainLoading(true);
            setOffset(0);
            setAllPosts([]);
          }}
        />
      ) : (
        /* Content state - inside ScrollView for normal scrolling */
        <ScrollView
          style={styles.postsList}
          contentContainerStyle={styles.postsListContent}
          showsVerticalScrollIndicator={false}
          onScroll={onScroll}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={activeTheme.colors.primary}
              colors={[activeTheme.colors.primary]}
            />
          }
        >
          {!isMainLoading && allPosts.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="newspaper-variant-outline" size={64} color="#666666" />
              <Text style={styles.noPostsTitle}>{t('explore.noPostsTitle')}</Text>
              <Text style={styles.noPostsText}>{t('explore.noPostsText')}</Text>
            </View>
          ) : (
            allPosts.map(renderPostCard)
          )}
          {isLoadingMore && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={activeTheme.colors.primary} />
              <Text style={styles.loadingText}>{t('explore.loadingMore')}</Text>
            </View>
          )}
        </ScrollView>
      )}

      <Portal>
        <Modal
          visible={filterVisible}
          onDismiss={hideFilterModal}
          contentContainerStyle={styles.modalContainer}
        >
          <View style={styles.modalContent}> 
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('explore.filters.title')}</Text>
              <TouchableOpacity onPress={resetFilters} style={styles.resetButton}>
                <Text style={[styles.resetButtonText, { color: activeTheme.colors.primary }]}>{t('explore.filters.reset')}</Text>
              </TouchableOpacity>
            </View>

            <ScrollView 
                style={styles.modalScrollContent}
                contentContainerStyle={styles.modalScrollContentContainer}
            >
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('explore.filters.postTypes')}</Text>
                <View style={styles.chipContainer}>
                  <Chip
                    key="all-types-chip"
                    mode="flat"
                    selected={tempFilters.postTypes.length === 0}
                    onPress={() => setTempFilters(prev => ({ ...prev, postTypes: [] }))}
                    style={[
                      styles.filterChip,
                      tempFilters.postTypes.length === 0 && { backgroundColor: activeTheme.colors.primary }
                    ]}
                    textStyle={[
                      styles.filterChipText,
                      tempFilters.postTypes.length === 0 && styles.selectedFilterChipText
                    ]}
                    showSelectedCheck={false}
                  >
                    {t('explore.filters.allPostTypes')} 
                  </Chip>
                  {availablePostTags.map((tag: PostTagPayload) => (
                    <Chip
                      key={tag.id}
                      mode="flat"
                      selected={tempFilters.postTypes.includes(tag.id)}
                      onPress={() => {
                        setTempFilters(prev => {
                          const newPostTypes = prev.postTypes.includes(tag.id)
                            ? prev.postTypes.filter(id => id !== tag.id)
                            : [...prev.postTypes, tag.id];
                          return { ...prev, postTypes: newPostTypes };
                        });
                      }}
                      style={[
                        styles.filterChip,
                        tempFilters.postTypes.includes(tag.id) && { backgroundColor: activeTheme.colors.primary }
                      ]}
                      textStyle={[
                        styles.filterChipText,
                        tempFilters.postTypes.includes(tag.id) && styles.selectedFilterChipText
                      ]}
                      showSelectedCheck={false}
                    >
                      {getTagName(tag, i18n.language)} 
                    </Chip>
                  ))}
                </View>
              </View>

              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('explore.filters.date.title')}</Text>
                <RadioButton.Group
                  onValueChange={handleDateRangeSelect}
                  value={tempFilters.dateRange[0]}
                >
                  {dateRangeOptions.map(option => (
                    <TouchableOpacity
                      key={option.id}
                      style={styles.radioItem}
                      onPress={() => handleDateRangeSelect(option.id)}
                    >
                      <RadioButton.Android value={option.id} color={activeTheme.colors.primary} />
                      <Text style={styles.radioLabel}>{option.label}</Text>
                    </TouchableOpacity>
                  ))}
                </RadioButton.Group>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                mode="outlined"
                onPress={hideFilterModal}
                style={[styles.modalButton, {borderColor: activeTheme.colors.primary}] }
                labelStyle={[styles.modalButtonLabel, {color: activeTheme.colors.primary}]}
              >
                {t('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={applyFilters}
                style={[styles.modalButton, { backgroundColor: activeTheme.colors.primary, borderRadius: 12 }]}
                labelStyle={[styles.applyButtonLabel, { color: activeTheme.colors.onPrimary }]}
              >
                {t('common.apply')}
              </Button>
            </View>
          </View>
        </Modal>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchBar: {
    flex: 1,
    elevation: 0,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    height: 42,
  },
  searchInput: { 
    fontSize: 15, 
    minHeight: 0, 
    lineHeight: Platform.OS === 'ios' ? undefined: 18,
  },
  filterButton: {
    width: 48,
    height: 42,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'transparent',
    height: '80%',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    height: '100%',
  },
  modalScrollContent: {
    flex: 1,
  },
  modalScrollContentContainer: {
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  resetButton: {
    padding: 8,
  },
  resetButtonText: {
    fontWeight: '500',
  },
  filterSection: {
    marginBottom: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterChip: {
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    height: 32,
  },
  filterChipText: {
    color: '#666666',
    fontSize: 15,
  },
  selectedFilterChipText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  radioLabel: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    borderRadius: 12,
  },
  modalButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  applyButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  postsList: {
    flex: 1,
  },
  postsListContent: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    gap: 16,
  },
  card: {
    borderRadius: 12,
    marginBottom: 4,
    marginHorizontal: 4,
    backgroundColor: '#FFFFFF',
  },
  cardWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  cardImageContainer: {
    overflow: 'hidden',
  },
  cardImage: {
    backgroundColor: '#FFFFFF',
    height: 160,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  cardContent: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    lineHeight: 24,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    color: '#666666',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
  },
  loadingText: {
    fontSize: 15,
    color: '#666666',
    marginTop: 16,
  },
  filterTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
  },
  noPostsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noPostsText: {
    fontSize: 15,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
});